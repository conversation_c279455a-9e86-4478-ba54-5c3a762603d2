import { _ as __vitePreload, C as COMMANDS, T as <PERSON>b<PERSON>ana<PERSON>, d as WorkonaTabManager, W as WorkspaceManager, S as StorageManager, a as WorkspaceSwitcher, M as MigrationManager } from './assets/dataMigration-BpGVSNzm.js';

if (typeof window === "undefined") {
  globalThis.window = {
    dispatchEvent: (event) => {
      console.log("Service Worker: 忽略 window.dispatchEvent 调用:", event.type);
      return true;
    }
  };
}
class BackgroundService {
  constructor() {
    this.init();
  }
  /**
   * 初始化后台服务
   */
  async init() {
    await this.setupSidePanel();
    this.setupCommandListeners();
    this.setupTabListeners();
    this.setupStorageListeners();
    await this.checkAndMigrateData();
    await this.clearActiveWorkspaceOnStartup();
    await this.initializeDefaultData();
    await this.startUserTabsRealTimeMonitoring();
    console.log("WorkSpace Pro background service initialized");
  }
  /**
   * 启动用户标签页实时监控
   */
  async startUserTabsRealTimeMonitoring() {
    try {
      const { UserTabsRealTimeMonitor } = await __vitePreload(async () => { const { UserTabsRealTimeMonitor } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.t);return { UserTabsRealTimeMonitor }},true?[]:void 0);
      UserTabsRealTimeMonitor.startMonitoring();
      console.log("📊 用户标签页实时监控已启动");
    } catch (error) {
      console.warn("启动用户标签页实时监控失败:", error);
    }
  }
  /**
   * 刷新用户标签页实时监控
   */
  async refreshUserTabsMonitoring() {
    try {
      const { WorkspaceSwitcher: WorkspaceSwitcher2 } = await __vitePreload(async () => { const { WorkspaceSwitcher: WorkspaceSwitcher2 } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.g);return { WorkspaceSwitcher: WorkspaceSwitcher2 }},true?[]:void 0);
      const activeWorkspaceResult = await WorkspaceSwitcher2.detectActiveWorkspace();
      if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
        const { UserTabsRealTimeMonitor } = await __vitePreload(async () => { const { UserTabsRealTimeMonitor } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.t);return { UserTabsRealTimeMonitor }},true?[]:void 0);
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspaceResult.data.id);
      }
    } catch (error) {
      console.warn("刷新用户标签页实时监控失败:", error);
    }
  }
  /**
   * 同步标签页编辑后的状态
   */
  async syncTabAfterEdit(tabId, changeInfo, _tab) {
    try {
      if (changeInfo.url || changeInfo.title) {
        const { WorkonaTabManager: WorkonaTabManager2 } = await __vitePreload(async () => { const { WorkonaTabManager: WorkonaTabManager2 } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.w);return { WorkonaTabManager: WorkonaTabManager2 }},true?[]:void 0);
        await WorkonaTabManager2.syncTabAfterEdit(tabId, changeInfo.url, changeInfo.title);
      }
    } catch (error) {
      console.warn("同步标签页编辑状态失败:", error);
    }
  }
  /**
   * 设置侧边栏
   */
  async setupSidePanel() {
    try {
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error("Failed to setup side panel:", error);
    }
  }
  /**
   * 设置命令监听器
   */
  setupCommandListeners() {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log("Command received:", command);
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          case "test_auto_classify":
            await this.testAutoClassify();
            break;
          default:
            console.log("Unknown command:", command);
        }
      } catch (error) {
        console.error("Error handling command:", command, error);
      }
    });
  }
  /**
   * 测试自动分类功能
   */
  async testAutoClassify() {
    try {
      console.log("🧪 [测试] 开始测试自动分类功能...");
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        console.log("❌ [测试] 没有找到活跃标签页");
        return;
      }
      const activeTab = tabs[0];
      console.log("🧪 [测试] 当前活跃标签页:", {
        id: activeTab.id,
        url: activeTab.url,
        title: activeTab.title
      });
      if (!activeTab.id || !activeTab.url) {
        console.log("❌ [测试] 活跃标签页缺少ID或URL");
        return;
      }
      console.log("🧪 [测试] 手动触发自动分类...");
      const result = await TabManager.autoClassifyNewTab(activeTab.id, activeTab.url);
      console.log("🧪 [测试] 自动分类结果:", result);
      const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      console.log("🧪 [测试] 验证 Workona ID:", verifyResult);
    } catch (error) {
      console.error("❌ [测试] 测试自动分类功能失败:", error);
    }
  }
  /**
   * 设置标签页监听器
   */
  setupTabListeners() {
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        console.log("标签页激活，记录状态变化");
        const { WorkspaceSessionManager } = await __vitePreload(async () => { const { WorkspaceSessionManager } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.f);return { WorkspaceSessionManager }},true?[]:void 0);
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          await WorkspaceSessionManager.setActiveTab(workonaIdResult.data);
        }
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error("Error handling tab activation:", error);
      }
    });
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        console.log("标签页位置变化，同步工作区状态");
        setTimeout(async () => {
          const { WorkspaceSessionManager } = await __vitePreload(async () => { const { WorkspaceSessionManager } = await import('./assets/dataMigration-BpGVSNzm.js').then(n => n.f);return { WorkspaceSessionManager }},true?[]:void 0);
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error("Error handling tab move:", error);
      }
    });
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log("🆕 [LISTENER] 标签页创建事件触发:", {
          id: tab.id,
          url: tab.url || "(no URL yet)",
          title: tab.title || "(no title yet)",
          status: tab.status,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        });
        if (tab.id && tab.url && !tab.url.includes("chrome://") && !tab.url.includes("chrome-extension://") && !tab.url.includes("about:") && tab.url !== "chrome://newtab/") {
          console.log(`🎯 [LISTENER] 标签页创建时尝试自动分类: ID=${tab.id}, URL=${tab.url}`);
          try {
            console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
            const result = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
            console.log(`✨ [LISTENER] 已自动分类新标签页: ${tab.url}`);
          } catch (error) {
            console.error("❌ [LISTENER] 自动分类新标签页失败:", error);
          }
        } else {
          console.log(`⏭️ 跳过标签页创建时的自动分类: ID=${tab.id}, URL=${tab.url}, 原因: ${!tab.id ? "无ID" : !tab.url ? "无URL" : tab.url.includes("chrome://") ? "系统页面" : tab.url.includes("chrome-extension://") ? "扩展页面" : tab.url.includes("about:") ? "about页面" : tab.url === "chrome://newtab/" ? "新标签页" : "未知原因"}`);
        }
        await this.notifyGlobalUserTabsStateChange("tab_created");
      } catch (error) {
        console.error("Error handling tab creation:", error);
      }
    });
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        if ((changeInfo.url || changeInfo.status === "complete") && tab.url) {
          console.log("🔄 [LISTENER] 标签页更新事件触发:", {
            id: tabId,
            url: tab.url,
            title: tab.title,
            status: tab.status,
            changeInfo,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          });
          const shouldClassify = tab.url && !tab.url.includes("chrome://") && !tab.url.includes("chrome-extension://") && !tab.url.includes("about:") && tab.url !== "chrome://newtab/" && tab.url !== "";
          if (shouldClassify) {
            console.log(`🎯 [LISTENER] 标签页更新时尝试自动分类: ID=${tabId}, URL=${tab.url}`);
            try {
              console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
              const result = await TabManager.autoClassifyNewTab(tabId, tab.url);
              console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
              console.log(`✨ [LISTENER] 已自动分类更新的标签页: ${tab.url}`);
            } catch (error) {
              console.error("❌ [LISTENER] 自动分类更新标签页失败:", error);
            }
          } else {
            console.log(`⏭️ 跳过标签页更新时的自动分类: ID=${tabId}, URL=${tab.url}, 原因: ${!tab.url ? "无URL" : tab.url.includes("chrome://") ? "系统页面" : tab.url.includes("chrome-extension://") ? "扩展页面" : tab.url.includes("about:") ? "about页面" : tab.url === "chrome://newtab/" ? "新标签页" : tab.url === "" ? "空URL" : "未知原因"}`);
          }
          await this.syncTabAfterEdit(tabId, changeInfo, tab);
          await this.notifyGlobalUserTabsStateChange("tab_updated");
        } else {
          console.log(`⏭️ 跳过标签页更新事件: ID=${tabId}, 原因: ${!changeInfo.url && changeInfo.status !== "complete" ? "非URL变化且非完成状态" : !tab.url ? "无URL" : "未知原因"}`);
        }
      } catch (error) {
        console.error("Error handling tab update:", error);
      }
    });
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log("Tab removed:", tabId);
        try {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;
            const workspaceId = workonaId.split("-")[1];
            await WorkspaceManager.removeWorkonaTabId(workspaceId, workonaId);
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理已删除标签页的 Workona ID: ${workonaId}`);
          }
        } catch (workonaError) {
          console.error("清理 Workona ID 映射失败:", workonaError);
        }
        await this.notifyGlobalUserTabsStateChange("tab_removed");
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab removal:", error);
      }
    });
    chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
      try {
        console.log("Tab moved:", tabId, moveInfo);
        await this.notifyGlobalUserTabsStateChange("tab_moved");
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab move:", error);
      }
    });
    chrome.tabs.onAttached.addListener(async (tabId, attachInfo) => {
      try {
        console.log("Tab attached:", tabId, attachInfo);
        await this.notifyGlobalUserTabsStateChange("tab_attached");
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab attach:", error);
      }
    });
    chrome.tabs.onDetached.addListener(async (tabId, detachInfo) => {
      try {
        console.log("Tab detached:", tabId, detachInfo);
        await this.notifyGlobalUserTabsStateChange("tab_detached");
      } catch (error) {
        console.error("Error handling tab detach:", error);
      }
    });
  }
  /**
   * 设置存储监听器
   */
  setupStorageListeners() {
    StorageManager.onChanged((changes) => {
      console.log("Storage changed:", changes);
      this.notifySidePanelUpdate(changes);
    });
  }
  /**
   * 清除活跃工作区状态，确保浏览器重启后不会自动选择工作区
   */
  async clearActiveWorkspaceOnStartup() {
    try {
      console.log("🔄 清除活跃工作区状态，让用户手动选择");
      await StorageManager.setActiveWorkspaceId(null);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map((workspace) => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
      }
      console.log("✅ 活跃工作区状态已清除");
    } catch (error) {
      console.error("❌ 清除活跃工作区状态失败:", error);
    }
  }
  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data.length === 0) {
        console.log("No workspaces found, creating default workspace templates");
      }
    } catch (error) {
      console.error("Error initializing default data:", error);
    }
  }
  /**
   * 根据索引切换工作区
   */
  async switchToWorkspaceByIndex(index) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("Failed to get workspaces:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error("Failed to switch workspace:", result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error("Error switching workspace by index:", error);
    }
  }
  /**
   * 切换侧边栏显示状态
   */
  async toggleSidePanel() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id;
        console.log("Toggle side panel for tab:", tabId);
      }
    } catch (error) {
      console.error("Error toggling side panel:", error);
    }
  }
  /**
   * 显示通知
   */
  showNotification(message, icon) {
    try {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "icons/icon-48.png",
        title: "WorkSpace Pro",
        message: `${icon || "🚀"} ${message}`
      });
      console.log("Notification shown:", message);
    } catch (error) {
      console.error("Error showing notification:", error);
    }
  }
  /**
   * 通知侧边栏更新
   */
  notifySidePanelUpdate(_changes) {
    try {
      console.log("Notifying side panel of storage changes");
    } catch (error) {
      console.error("Error notifying side panel:", error);
    }
  }
  /**
   * 通知全局用户标签页状态变化
   */
  async notifyGlobalUserTabsStateChange(eventType) {
    try {
      setTimeout(async () => {
        try {
          if (typeof chrome !== "undefined" && chrome.runtime) {
            chrome.runtime.sendMessage({
              type: "USER_TABS_VISIBILITY_CHANGED",
              workspaceId: "global",
              eventType
            }).catch((error) => {
              console.log("发送用户标签页状态变化消息失败:", error);
            });
          }
          console.log(`📢 已通知全局用户标签页状态变化: ${eventType}`);
        } catch (error) {
          console.error("发送全局用户标签页状态变化通知失败:", error);
        }
      }, 50);
    } catch (error) {
      console.error("通知全局用户标签页状态变化失败:", error);
    }
  }
  /**
   * 检查并执行数据迁移
   */
  async checkAndMigrateData() {
    try {
      console.log("🔍 检查数据迁移需求...");
      const versionResult = await MigrationManager.detectDataVersion();
      if (!versionResult.success) {
        console.error("检测数据版本失败:", versionResult.error);
        return;
      }
      const currentVersion = versionResult.data;
      console.log(`📊 当前数据版本: ${currentVersion}`);
      if (currentVersion !== "1.0.0") {
        console.log("🚀 开始执行数据迁移...");
        const migrationResult = await MigrationManager.migrateToWorkonaFormat({
          backupOriginalData: true,
          validateAfterMigration: true,
          rollbackOnError: true,
          preserveUserPreferences: true
        });
        if (migrationResult.success) {
          if (migrationResult.data) {
            console.log("✅ 基础数据迁移成功完成");
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          } else {
            console.log("ℹ️ 数据已是最新版本，无需迁移");
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          }
        } else {
          console.error("❌ 数据迁移失败:", migrationResult.error);
        }
      } else {
        console.log("✅ 数据版本已是最新，无需迁移");
      }
    } catch (error) {
      console.error("❌ 数据迁移检查过程中发生错误:", error);
    }
  }
}
new BackgroundService();
