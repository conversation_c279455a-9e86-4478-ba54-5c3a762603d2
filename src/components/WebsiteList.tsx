import React, { useState, useEffect } from 'react';
import { ExternalLink, X, Edit, Check, Trash2, Pin, PinOff } from 'lucide-react';
import { Website } from '@/types/workspace';

interface WebsiteListProps {
  websites: Website[];
  onRemoveWebsite: (websiteId: string) => void;
  onEditWebsite: (website: Website) => void;
  onReorderWebsites?: (websiteIds: string[]) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
  onBatchPin?: (websiteIds: string[]) => void;
  onBatchUnpin?: (websiteIds: string[]) => void;
  batchMode?: boolean;
  onExitBatchMode?: () => void;
}

/**
 * 网站列表组件
 */
const WebsiteList: React.FC<WebsiteListProps> = ({
  websites,
  onRemoveWebsite,
  onEditWebsite,
  onReorderWebsites: _onReorderWebsites,
  onBatchDelete,
  onBatchPin,
  onBatchUnpin,
  batchMode = false,
  onExitBatchMode,
}) => {
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(batchMode);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [pinnedWebsites, setPinnedWebsites] = useState<Set<string>>(new Set());
  const [openTabStates, setOpenTabStates] = useState<Record<string, { isOpen: boolean; chromeId?: number; workonaId?: string }>>({});



  // 同步批量模式状态
  useEffect(() => {
    setIsSelectionMode(batchMode);
    if (!batchMode) {
      setSelectedWebsites(new Set());
    }
  }, [batchMode]);

  // 实时检查标签页状态
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const checkTabStates = async () => {
      try {
        const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
        const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

        // 获取当前活跃工作区
        const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
        if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
          return;
        }

        const workspaceId = activeWorkspaceResult.data.id;
        const websiteIds = websites.map(w => w.id);

        // 批量检查所有网站的标签页状态
        const statesResult = await WorkonaTabManager.getWorkspaceWebsiteTabStates(workspaceId, websiteIds);
        if (statesResult.success) {
          setOpenTabStates(statesResult.data!);
        }
      } catch (error) {
        console.warn('检查标签页状态失败:', error);
      }
    };

    // 立即检查一次
    checkTabStates();

    // 每秒检查一次状态
    intervalId = setInterval(checkTabStates, 1000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [websites]);

  // 检查所有网站的固定状态
  useEffect(() => {
    const checkPinnedStatus = async () => {
      const pinnedSet = new Set<string>();

      for (const website of websites) {
        const isPinned = await isWebsitePinned(website);
        if (isPinned) {
          pinnedSet.add(website.id);
        }
      }

      setPinnedWebsites(pinnedSet);
    };

    if (websites.length > 0) {
      checkPinnedStatus();
    }
  }, [websites]);

  // 点击外部关闭更多菜单
  useEffect(() => {
    const handleClickOutside = (_event: MouseEvent) => {
      if (showMoreMenu) {
        setShowMoreMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showMoreMenu]);

  /**
   * 智能重复检测和固定逻辑
   */
  const findAndHandleExistingTab = async (website: Website): Promise<{ found: boolean; tabId?: number }> => {
    try {
      console.log('🔍 检查重复标签页:', website.url);

      // 1. 精确URL匹配
      let tabs = await chrome.tabs.query({ url: website.url });

      // 2. 如果精确匹配失败，尝试通配符匹配
      if (tabs.length === 0) {
        tabs = await chrome.tabs.query({ url: website.url + '*' });
      }

      // 3. 如果还是没找到，尝试域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(website.url).hostname;
          const allTabs = await chrome.tabs.query({ currentWindow: true });

          tabs = allTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch (error) {
          console.warn('域名匹配失败:', error);
        }
      }

      if (tabs.length > 0) {
        const existingTab = tabs[0];
        console.log('✅ 找到现有标签页:', existingTab.title, existingTab.pinned ? '(已固定)' : '(未固定)');

        // Workona 风格：不再处理固定状态

        // 激活标签页
        await chrome.tabs.update(existingTab.id!, { active: true });
        console.log('🎯 激活现有标签页');

        return { found: true, tabId: existingTab.id };
      }

      return { found: false };
    } catch (error) {
      console.error('检查重复标签页失败:', error);
      return { found: false };
    }
  };

  /**
   * 处理网站点击
   */
  const handleWebsiteClick = async (website: Website) => {
    try {
      console.log('🔍 点击网站:', website.title, website.url);

      // 智能重复检测
      const existingResult = await findAndHandleExistingTab(website);

      if (!existingResult.found) {
        // 没有找到现有标签页，创建新的
        console.log('🆕 创建新标签页');
        const newTab = await chrome.tabs.create({
          url: website.url,
          pinned: false, // Workona 风格：不使用固定状态
          active: true
        });

        // 立即更新标签页状态
        if (newTab.id) {
          setTimeout(async () => {
            try {
              const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
              const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

              // 获取当前活跃工作区
              const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
              if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
                const workspaceId = activeWorkspaceResult.data.id;
                const websiteIds = websites.map(w => w.id);

                // 重新检查标签页状态
                const statesResult = await WorkonaTabManager.getWorkspaceWebsiteTabStates(workspaceId, websiteIds);
                if (statesResult.success) {
                  setOpenTabStates(statesResult.data!);
                }
              }
            } catch (error) {
              console.warn('更新标签页状态失败:', error);
            }
          }, 500); // 延迟500ms确保标签页完全创建
        }

        console.log('✅ 新标签页创建完成');
      }
    } catch (error) {
      console.error('Failed to open website:', error);
    }
  };

  // Workona 风格：移除固定状态切换功能

  /**
   * 处理移除网站
   */
  const handleRemoveWebsite = (e: React.MouseEvent, websiteId: string) => {
    e.stopPropagation();
    onRemoveWebsite(websiteId);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    onEditWebsite(website);
  };

  // Workona 风格：移除选择模式切换功能

  /**
   * 处理网站选择
   */
  const handleWebsiteSelect = (websiteId: string) => {
    const newSelected = new Set(selectedWebsites);
    if (newSelected.has(websiteId)) {
      newSelected.delete(websiteId);
    } else {
      newSelected.add(websiteId);
    }
    setSelectedWebsites(newSelected);
  };

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = () => {
    if (selectedWebsites.size === websites.length) {
      setSelectedWebsites(new Set());
    } else {
      setSelectedWebsites(new Set(websites.map(w => w.id)));
    }
  };

  // Workona 风格：移除批量固定功能

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchDelete) {
      onBatchDelete(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    setSelectedWebsites(new Set());
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchPin) {
      onBatchPin(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    setSelectedWebsites(new Set());
  };

  /**
   * 处理批量取消固定
   */
  const handleBatchUnpin = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchUnpin) {
      onBatchUnpin(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    setSelectedWebsites(new Set());
  };

  /**
   * 处理单个网站的固定操作
   */
  const handlePinWebsite = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    try {
      // 通过Workona ID映射查找对应的标签页并固定
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        console.warn('无法获取当前工作区');
        return;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && !tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: true });
                console.log(`📌 固定标签页: ${tab.title}`);
                // 更新状态
                setPinnedWebsites(prev => new Set(prev).add(website.id));
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 固定标签页失败:', error);
    }
  };

  /**
   * 处理单个网站的取消固定操作
   */
  const handleUnpinWebsite = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    try {
      // 通过Workona ID映射查找对应的标签页并取消固定
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        console.warn('无法获取当前工作区');
        return;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: false });
                console.log(`📌 取消固定标签页: ${tab.title}`);
                // 更新状态
                setPinnedWebsites(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(website.id);
                  return newSet;
                });
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 取消固定标签页失败:', error);
    }
  };

  /**
   * 检查网站对应的标签页是否已固定
   */
  const isWebsitePinned = async (website: Website): Promise<boolean> => {
    try {
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        return false;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        return false;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              return tab?.pinned || false;
            }
          }
        } catch (error) {
          // 标签页可能不存在，继续检查其他标签页
          continue;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ 检查固定状态失败:', error);
      return false;
    }
  };

  /**
   * 获取网站图标
   */
  const getWebsiteIcon = (website: Website) => {
    if (website.favicon && website.favicon !== '') {
      return (
        <img
          src={website.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }

    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // 按order字段排序
  const sortedWebsites = [...websites].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-1">
      {/* 批量操作工具栏 - 只在批量模式下显示 */}
      {websites.length > 0 && batchMode && isSelectionMode && (
        <div className="mb-2 relative">
          <div className="flex items-center justify-between px-1 py-1.5 bg-slate-700/80 rounded border border-slate-600/50">
            <div className="flex items-center gap-2">
              {isSelectionMode && (
                <>
                  <button
                    onClick={handleSelectAll}
                    className="px-2 py-1 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded text-xs transition-all duration-200"
                  >
                    {selectedWebsites.size === websites.length ? '取消' : '全选'}
                  </button>

                  <div className="flex items-center gap-1 px-1.5 py-1 bg-slate-800/50 rounded">
                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-slate-300">
                      {selectedWebsites.size}
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* 右侧按钮组 */}
            <div className="flex items-center gap-1">
              {/* 批量操作按钮 */}
              {selectedWebsites.size > 0 && (
                <>
                  <button
                    onClick={handleBatchPin}
                    className="flex items-center justify-center w-6 h-6 bg-blue-600 text-white hover:bg-blue-700 rounded transition-all duration-200"
                    title="批量固定"
                  >
                    <Pin className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleBatchUnpin}
                    className="flex items-center justify-center w-6 h-6 bg-slate-600 text-white hover:bg-slate-700 rounded transition-all duration-200"
                    title="批量取消固定"
                  >
                    <PinOff className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleBatchDelete}
                    className="flex items-center justify-center w-6 h-6 bg-red-600 text-white hover:bg-red-700 rounded transition-all duration-200"
                    title="删除选中"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </>
              )}

              {/* 退出批量模式按钮 */}
              <button
                onClick={onExitBatchMode}
                className="flex items-center justify-center w-6 h-6 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded transition-all duration-200"
                title="退出批量模式"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      )}

      {sortedWebsites.map((website) => {
        const isSelected = selectedWebsites.has(website.id);

        return (
        <div
          key={website.id}
          className={`website-item ${isSelected ? 'bg-blue-600/20 border-blue-500' : ''}`}
          onClick={() => isSelectionMode ? handleWebsiteSelect(website.id) : handleWebsiteClick(website)}
        >
          {/* 选择框 */}
          {isSelectionMode && (
            <div className="flex-shrink-0">
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                isSelected
                  ? 'bg-blue-600 border-blue-600'
                  : 'border-slate-400'
              }`}>
                {isSelected && <Check className="w-3 h-3 text-white" />}
              </div>
            </div>
          )}

          {/* 网站图标 */}
          <div className="flex-shrink-0">
            {getWebsiteIcon(website)}
          </div>

          {/* 网站信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm text-white truncate">
                {website.title}
              </span>
              {/* 标签页状态指示器 - 与工作区活跃状态指示器完全一致的样式 */}
              {openTabStates[website.id]?.isOpen && (
                <div
                  className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0"
                  title="标签页已打开"
                />
              )}
            </div>
            <p className="text-xs text-slate-400 truncate">
              {formatUrl(website.url)}
            </p>
          </div>

          {/* 操作按钮 */}
          {!isSelectionMode && (
            <div className="website-actions flex items-center gap-1">
              {/* 固定/取消固定按钮 */}
              {pinnedWebsites.has(website.id) ? (
                <button
                  onClick={(e) => handleUnpinWebsite(e, website)}
                  className="p-1 hover:bg-orange-600 rounded transition-colors duration-150"
                  title="取消固定标签页"
                >
                  <PinOff className="w-3 h-3 text-orange-400 hover:text-white" />
                </button>
              ) : (
                <button
                  onClick={(e) => handlePinWebsite(e, website)}
                  className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                  title="固定标签页"
                >
                  <Pin className="w-3 h-3 text-slate-400 hover:text-white" />
                </button>
              )}

              <button
                onClick={async (e) => {
                  e.stopPropagation();

                  try {
                    console.log('🔍 在新标签页中打开 (Workona 风格):', website.url);

                    // 使用智能重复检测
                    const existingResult = await findAndHandleExistingTab(website);

                    if (!existingResult.found) {
                      // 没有找到现有标签页，创建新的工作区核心标签页
                      const tab = await chrome.tabs.create({
                        url: website.url,
                        pinned: false,  // Workona 风格：不使用固定状态
                        active: true
                      });

                      // 概念性重构：为工作区网站创建核心标签页 Workona ID 映射
                      if (tab.id) {
                        try {
                          const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
                          const { WorkspaceSwitcher } = await import('@/utils/workspaceSwitcher');

                          // 获取当前活跃工作区
                          const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
                          if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
                            const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspaceResult.data.id);

                            const mappingResult = await WorkonaTabManager.createTabIdMapping(
                              workonaId,
                              tab.id,
                              activeWorkspaceResult.data.id,
                              website.id,
                              {
                                isWorkspaceCore: true, // 概念性重构：标记为核心标签页
                                source: 'workspace_website'
                              }
                            );

                            if (mappingResult.success) {
                              console.log(`✅ 为工作区网站创建核心标签页 Workona ID: ${workonaId}`);
                            }
                          }
                        } catch (error) {
                          console.error('创建工作区核心标签页映射失败:', error);
                        }
                      }

                      console.log('✅ 在新标签页中打开工作区核心标签页:', website.url);
                    }
                  } catch (error) {
                    console.error('在新标签页中打开失败:', error);
                    // 降级到window.open
                    window.open(website.url, '_blank');
                  }
                }}
                className="p-1 hover:bg-slate-500 rounded transition-colors duration-150"
                title="在新标签页中打开"
              >
                <ExternalLink className="w-3 h-3 text-slate-400" />
              </button>
              <button
                onClick={(e) => handleEditWebsite(e, website)}
                className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                title="编辑网站"
              >
                <Edit className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
              <button
                onClick={(e) => handleRemoveWebsite(e, website.id)}
                className="p-1 hover:bg-red-600 rounded transition-colors duration-150"
                title="移除网站"
              >
                <X className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
            </div>
          )}
        </div>
        );
      })}
    </div>
  );
};

export default WebsiteList;
