import {
  WorkSpace,
  Website,
  CreateWorkspaceOptions,
  UpdateWorkspaceOptions,
  AddWebsiteOptions,
  OperationResult,
  WorkspaceType,
  WorkspaceState
} from '@/types/workspace';
import { StorageManager } from './storage';
import { WindowManager } from './windowManager';
import { WorkonaTabManager } from './workonaTabManager';
import {
  ERROR_CODES,
  WORKSPACE_COLORS,
  WORKSPACE_ICONS,
  URL_REGEX,
  DEFAULT_FAVICON
} from './constants';

/**
 * 工作区管理类
 */
export class WorkspaceManager {
  /**
   * 生成唯一ID（支持 Workona 风格）
   */
  private static generateId(type: WorkspaceType = 'saved'): string {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 9);

    // Workona 风格：临时工作区使用 temp_ 前缀
    if (type === 'temp') {
      return `temp_${timestamp}_${randomStr}`;
    }

    // 保持现有格式用于已保存和未保存的工作区
    return `ws_${timestamp}_${randomStr}`;
  }

  /**
   * 生成网站ID
   */
  private static generateWebsiteId(): string {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证URL格式
   */
  private static isValidUrl(url: string): boolean {
    return URL_REGEX.test(url);
  }

  /**
   * 获取网站favicon
   */
  private static async getFavicon(url: string): Promise<string> {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }

  /**
   * 获取网站标题
   */
  private static async getWebsiteTitle(url: string): Promise<string> {
    try {
      // 尝试从当前标签页获取标题
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      
      // 从URL提取域名作为标题
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return url;
    }
  }

  /**
   * 创建新工作区
   */
  static async createWorkspace(options: CreateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      // 验证工作区名称
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Workspace name cannot be empty',
          },
        };
      }

      // 获取现有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }

      const existingWorkspaces = workspacesResult.data!;

      // 检查重复名称
      if (existingWorkspaces.some(w => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: 'Workspace with this name already exists',
          },
        };
      }

      // 确定工作区类型（Workona 风格）
      const workspaceType: WorkspaceType = (options as any).type || 'saved';

      // 创建工作区
      const workspace: WorkSpace = {
        id: this.generateId(workspaceType),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length,

        // Workona 风格扩展字段
        type: workspaceType,
        pos: Date.now(), // 使用时间戳作为位置标识符
        state: 'inactive' as WorkspaceState,
        workonaTabIds: [],
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: []
      };

      // 添加网站
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website: Website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            // Workona 风格：移除 isPinned 字段，完全基于 Workona ID 映射管理
            addedAt: Date.now(),
            order: i,
          };
          workspace.websites.push(website);
        }
      }

      // 保存工作区
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }

      // 如果选择了添加当前标签页，获取并添加当前窗口的所有标签页
      if (options.addCurrentTabs) {
        console.log(`📋 添加当前标签页到新工作区: ${workspace.name}`);
        await this.addCurrentTabsToWorkspace(workspace.id);
      }

      // 如果需要激活
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }

      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区
   */
  static async updateWorkspace(id: string, options: UpdateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`,
          },
        };
      }

      const workspace = workspaces[workspaceIndex];

      // 更新字段
      if (options.name !== undefined) workspace.name = options.name;
      if (options.icon !== undefined) workspace.icon = options.icon;
      if (options.color !== undefined) workspace.color = options.color;
      if (options.websites !== undefined) workspace.websites = options.websites;
      if (options.isActive !== undefined) workspace.isActive = options.isActive;
      
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 删除工作区
   */
  static async deleteWorkspace(id: string): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`,
          },
        };
      }

      // 关闭工作区的专用窗口
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
        // 继续执行，不要因为窗口关闭失败而阻止工作区删除
      }

      // 移除工作区
      workspaces.splice(workspaceIndex, 1);

      // 重新排序
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      // 如果删除的是活跃工作区，清除活跃状态
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to delete workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId: string, url: string, options: AddWebsiteOptions = {}): Promise<OperationResult<Website>> {
    try {
      // 验证URL
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Invalid URL format',
          },
        };
      }

      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      // 检查重复URL
      if (workspace.websites.some(w => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: 'Website with this URL already exists in workspace',
          },
        };
      }

      // 获取网站标题（如果未提供）
      const websiteTitle = options.title || await this.getWebsiteTitle(url);

      // 创建网站对象（Workona 风格：移除固定标签页依赖）
      const website: Website = {
        id: this.generateWebsiteId(),
        url,
        title: websiteTitle,
        favicon: options.favicon || await this.getFavicon(url),
        // Workona 风格：移除 isPinned 字段，完全基于 Workona ID 映射管理
        addedAt: Date.now(),
        order: workspace.websites.length,
      };

      // 添加到工作区
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 检查是否有现有标签页匹配这个URL，如果有则转换其 Workona ID 类型
      await this.promoteExistingTabToWorkspaceCore(workspaceId, website.id, url);

      // 如果需要在新标签页中打开，使用 Workona 标签页管理（概念性重构：核心标签页）
      if (options.openInNewTab) {
        console.log(`🚀 创建工作区核心标签页: ${url}`);

        try {
          // 创建标签页
          const tab = await chrome.tabs.create({
            url,
            pinned: false, // Workona 风格：不使用固定状态
            active: true
          });

          if (tab.id) {
            // 为核心标签页创建 Workona ID 映射
            const { WorkonaTabManager } = await import('./workonaTabManager');
            const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);

            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaId,
              tab.id,
              workspaceId,
              website.id,
              {
                isWorkspaceCore: true, // 概念性重构：标记为核心标签页
                source: 'workspace_website'
              }
            );

            if (mappingResult.success) {
              // 将 Workona 标签页ID添加到工作区
              await this.addWorkonaTabId(workspaceId, workonaId);
              console.log(`✅ 成功创建工作区核心标签页: ${workonaId} (网站: ${website.id})`);
            } else {
              console.warn('Workona ID 映射创建失败:', mappingResult.error);
            }
          }
        } catch (error) {
          console.error('创建工作区核心标签页失败:', error);
        }
      }

      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add website',
          details: error,
        },
      };
    }
  }

  /**
   * 将现有标签页提升为工作区核心标签页
   * 用于"添加当前标签页"功能，将会话临时标签页转换为工作区核心标签页
   */
  private static async promoteExistingTabToWorkspaceCore(workspaceId: string, websiteId: string, url: string): Promise<void> {
    try {
      console.log(`🔄 检查是否有现有标签页需要提升为工作区核心: ${url}`);

      // 获取所有标签页
      const { TabManager } = await import('./tabs');
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        console.warn('获取标签页列表失败:', allTabsResult.error);
        return;
      }

      const allTabs = allTabsResult.data!;

      // 查找匹配URL的标签页
      const matchingTabs = allTabs.filter(tab => {
        try {
          // 标准化URL进行比较
          const normalizeUrl = (url: string) => url.replace(/\/$/, '').toLowerCase();
          return normalizeUrl(tab.url).startsWith(normalizeUrl(url));
        } catch {
          return false;
        }
      });

      if (matchingTabs.length === 0) {
        console.log(`📝 没有找到匹配URL的现有标签页: ${url}`);
        return;
      }

      console.log(`🎯 找到 ${matchingTabs.length} 个匹配的标签页，开始检查和转换...`);

      const { WorkonaTabManager } = await import('./workonaTabManager');

      for (const tab of matchingTabs) {
        try {
          // 检查标签页是否已有 Workona ID
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

          if (workonaIdResult.success && workonaIdResult.data) {
            // 检查是否为会话临时标签页
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);

            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore, tabType } = metadataResult.data;

              if (!isWorkspaceCore && tabType === 'session') {
                // 这是一个会话临时标签页，需要提升为工作区核心
                console.log(`⬆️ 提升会话临时标签页为工作区核心: ${tab.title} (${workonaIdResult.data})`);

                const promoteResult = await WorkonaTabManager.promoteToWorkspaceCore(
                  workonaIdResult.data,
                  websiteId
                );

                if (promoteResult.success) {
                  console.log(`✅ 成功提升标签页: ${tab.title} -> 工作区核心 (网站ID: ${websiteId})`);
                } else {
                  console.error(`❌ 提升标签页失败:`, promoteResult.error);
                }
              } else if (isWorkspaceCore) {
                console.log(`ℹ️ 标签页已经是工作区核心: ${tab.title} (${workonaIdResult.data})`);
              }
            }
          } else {
            // 标签页没有 Workona ID，为其创建一个工作区核心 Workona ID
            console.log(`🆔 为现有标签页创建工作区核心 Workona ID: ${tab.title}`);

            const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              newWorkonaId,
              tab.id,
              workspaceId,
              websiteId,
              {
                isWorkspaceCore: true,
                source: 'workspace_website'
              }
            );

            if (mappingResult.success) {
              console.log(`✅ 为现有标签页创建工作区核心 Workona ID: ${newWorkonaId} (${tab.title})`);
            } else {
              console.error(`❌ 创建 Workona ID 失败:`, mappingResult.error);
            }
          }
        } catch (error) {
          console.error(`❌ 处理标签页 ${tab.id} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 提升现有标签页为工作区核心时出错:', error);
    }
  }

  /**
   * 将当前窗口的所有标签页添加到工作区
   */
  private static async addCurrentTabsToWorkspace(workspaceId: string): Promise<void> {
    try {
      console.log(`📋 开始添加当前标签页到工作区: ${workspaceId}`);

      // 获取当前窗口的所有标签页
      const { TabManager } = await import('./tabs');
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.error('获取当前窗口标签页失败:', currentTabsResult.error);
        return;
      }

      const currentTabs = currentTabsResult.data!;
      console.log(`🔍 找到 ${currentTabs.length} 个当前窗口标签页`);

      // 过滤掉系统页面和扩展页面
      const validTabs = currentTabs.filter(tab => {
        return !tab.url.startsWith('chrome://') &&
               !tab.url.startsWith('chrome-extension://') &&
               !tab.url.startsWith('edge://') &&
               !tab.url.startsWith('about:') &&
               !tab.url.includes('workspace-placeholder.html') &&
               tab.url !== 'chrome://newtab/' &&
               tab.url.trim() !== '';
      });

      console.log(`✅ 过滤后有效标签页: ${validTabs.length} 个`);

      if (validTabs.length === 0) {
        console.log('⚠️ 没有有效的标签页可以添加到工作区');
        return;
      }

      // 为每个有效标签页添加到工作区
      let addedCount = 0;
      for (const tab of validTabs) {
        try {
          console.log(`📝 添加标签页到工作区: ${tab.title} (${tab.url})`);

          const addResult = await this.addWebsite(workspaceId, tab.url, {
            title: tab.title,
            favicon: tab.favicon,
            openInNewTab: false // 不创建新标签页，使用现有的
          });

          if (addResult.success) {
            addedCount++;
            console.log(`✅ 成功添加: ${tab.title}`);
          } else {
            console.warn(`⚠️ 添加失败: ${tab.title} - ${addResult.error?.message}`);
          }
        } catch (error) {
          console.error(`❌ 添加标签页时出错: ${tab.title}`, error);
        }
      }

      console.log(`🎉 成功添加 ${addedCount}/${validTabs.length} 个标签页到工作区 ${workspaceId}`);
    } catch (error) {
      console.error('❌ 添加当前标签页到工作区时出错:', error);
    }
  }

  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId: string, websiteId: string): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      const websiteIndex = workspace.websites.findIndex(w => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`,
          },
        };
      }

      // 在移除网站前，先处理相关的工作区专属标签页降级
      const removedWebsite = workspace.websites[websiteIndex];
      console.log(`🔄 处理网站 "${removedWebsite.title}" 相关的工作区专属标签页降级`);

      try {
        // 查找与该网站关联的工作区专属标签页
        const { WorkonaTabManager } = await import('./workonaTabManager');
        const mappingsResult = await StorageManager.getTabIdMappings();

        if (mappingsResult.success) {
          const mappings = mappingsResult.data!;
          const relatedMappings = mappings.filter(mapping =>
            mapping.workspaceId === workspaceId &&
            mapping.websiteId === websiteId &&
            mapping.isWorkspaceCore
          );

          console.log(`🔍 找到 ${relatedMappings.length} 个与网站 "${removedWebsite.title}" 关联的工作区专属标签页`);

          // 降级每个相关的工作区专属标签页
          for (const mapping of relatedMappings) {
            const demoteResult = await WorkonaTabManager.demoteToSessionTab(mapping.workonaId);
            if (demoteResult.success) {
              console.log(`⬇️ 成功降级标签页: ${mapping.workonaId} (网站: ${removedWebsite.title})`);
            } else {
              console.warn(`⚠️ 降级标签页失败: ${mapping.workonaId}`, demoteResult.error);
            }
          }
        }
      } catch (error) {
        console.warn('处理工作区专属标签页降级时出错:', error);
        // 不阻断网站移除流程
      }

      // 移除网站
      workspace.websites.splice(websiteIndex, 1);

      // 重新排序
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });

      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      console.log(`✅ 成功移除网站 "${removedWebsite.title}" 并处理相关标签页降级`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove website',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }): Promise<OperationResult<Website>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      const website = workspace.websites.find(w => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`,
          },
        };
      }

      // 更新网站信息
      if (updates.url !== undefined) {
        // 验证URL格式
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: 'Invalid URL format',
            },
          };
        }
        website.url = updates.url;
        // 如果URL改变了，更新favicon
        website.favicon = await this.getFavicon(updates.url);
      }

      if (updates.title !== undefined) {
        website.title = updates.title;
      }

      if (updates.isPinned !== undefined) {
        website.isPinned = updates.isPinned;
      }

      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }

      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update website',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds: string[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;

      // 按新顺序重新排列
      const reorderedWorkspaces: WorkSpace[] = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find(w => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId: string, websiteIds: string[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      // 按新顺序重新排列网站
      const reorderedWebsites: Website[] = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find(w => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });

      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder websites',
          details: error,
        },
      };
    }
  }

  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(
    workspaceId: string,
    isHidden: boolean,
    hiddenTabIds?: number[]
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error,
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新隐藏状态
      workspace.userTabsHidden = isHidden;
      workspace.hiddenUserTabIds = hiddenTabIds || [];
      workspace.updatedAt = Date.now();

      // 用户标签页隐藏状态已更新

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
  }>> {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return {
          success: false,
          error: workspaceResult.error,
        };
      }

      const workspace = workspaceResult.data!;

      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenUserTabIds || [],
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 清除工作区的隐藏标签页记录（当标签页被永久删除时调用）
   */
  static async clearHiddenTabIds(workspaceId: string, tabIdsToRemove: number[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error,
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 从隐藏列表中移除指定的标签页ID
      if (workspace.hiddenUserTabIds) {
        workspace.hiddenUserTabIds = workspace.hiddenUserTabIds.filter(
          id => !tabIdsToRemove.includes(id)
        );
        workspace.updatedAt = Date.now();

        console.log(`从工作区 "${workspace.name}" 的隐藏列表中移除标签页ID: ${tabIdsToRemove.join(', ')}`);

        // 保存更改
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) return saveResult;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear hidden tab IDs',
          details: error,
        },
      };
    }
  }

  // ===== Workona 风格工作区管理方法 =====

  /**
   * 更新工作区类型（Workona 风格）
   */
  static async updateWorkspaceType(
    workspaceId: string,
    newType: WorkspaceType
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新工作区类型
      workspace.type = newType;
      workspace.updatedAt = Date.now();

      // 如果从临时转为保存，更新ID格式
      if (workspace.type === 'temp' && newType === 'saved') {
        const newId = this.generateId('saved');

        // 更新所有相关的 Workona 标签页映射
        const clearResult = await WorkonaTabManager.clearWorkspaceTabMappings(workspaceId);
        if (clearResult.success) {
          console.log(`清理了 ${clearResult.data} 个旧的标签页映射`);
        }

        workspace.id = newId;
        console.log(`工作区类型转换: ${workspaceId} -> ${newId} (${newType})`);
      }

      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区类型已更新: ${workspace.name} -> ${newType}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace type',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区位置标识符（Workona 风格排序）
   */
  static async updateWorkspacePosition(
    workspaceId: string,
    newPosition: number
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新位置标识符
      workspace.pos = newPosition;
      workspace.updatedAt = Date.now();

      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`📍 工作区位置已更新: ${workspace.name} -> ${newPosition}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace position',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区状态（Workona 风格）
   */
  static async updateWorkspaceState(
    workspaceId: string,
    newState: WorkspaceState
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新状态
      workspace.state = newState;
      workspace.updatedAt = Date.now();

      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🔄 工作区状态已更新: ${workspace.name} -> ${newState}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace state',
          details: error,
        },
      };
    }
  }

  /**
   * 添加 Workona 标签页ID到工作区
   */
  static async addWorkonaTabId(
    workspaceId: string,
    workonaTabId: string
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 初始化 workonaTabIds 如果不存在
      if (!workspace.workonaTabIds) {
        workspace.workonaTabIds = [];
      }

      // 添加 Workona 标签页ID（避免重复）
      if (!workspace.workonaTabIds.includes(workonaTabId)) {
        workspace.workonaTabIds.push(workonaTabId);
        workspace.updatedAt = Date.now();

        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }

        console.log(`➕ 添加 Workona 标签页ID: ${workonaTabId} -> ${workspace.name}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add Workona tab ID',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区移除 Workona 标签页ID
   */
  static async removeWorkonaTabId(
    workspaceId: string,
    workonaTabId: string
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace || !workspace.workonaTabIds) {
        return { success: true }; // 没有找到或没有标签页ID，认为操作成功
      }

      // 移除 Workona 标签页ID
      const initialLength = workspace.workonaTabIds.length;
      workspace.workonaTabIds = workspace.workonaTabIds.filter(id => id !== workonaTabId);

      if (workspace.workonaTabIds.length !== initialLength) {
        workspace.updatedAt = Date.now();

        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }

        console.log(`➖ 移除 Workona 标签页ID: ${workonaTabId} <- ${workspace.name}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove Workona tab ID',
          details: error,
        },
      };
    }
  }

  /**
   * 清理临时工作区（生命周期管理）
   */
  static async cleanupTemporaryWorkspaces(maxAge: number = 24 * 60 * 60 * 1000): Promise<OperationResult<number>> {
    try {
      console.log('🧹 开始清理临时工作区...');

      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const now = Date.now();
      let cleanedCount = 0;

      // 找到需要清理的临时工作区
      const workspacesToKeep = workspaces.filter(workspace => {
        if (workspace.type === 'temp') {
          const age = now - workspace.createdAt;
          if (age > maxAge) {
            console.log(`🗑️ 清理过期临时工作区: ${workspace.name} (${Math.round(age / (60 * 60 * 1000))}小时前创建)`);
            cleanedCount++;
            return false; // 不保留
          }
        }
        return true; // 保留
      });

      // 如果有工作区被清理，保存更新
      if (cleanedCount > 0) {
        const saveResult = await StorageManager.saveWorkspaces(workspacesToKeep);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }

      console.log(`✅ 临时工作区清理完成，清理了 ${cleanedCount} 个过期工作区`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to cleanup temporary workspaces',
          details: error,
        },
      };
    }
  }
}
