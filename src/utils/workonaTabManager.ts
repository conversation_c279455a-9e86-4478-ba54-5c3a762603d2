import {
  WorkspaceTab,
  TabIdMapping,
  OperationResult,
  WorkonaTabCreateOptions,
  WorkonaTabMatchResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';

/**
 * Workona 风格标签页ID管理器
 * 实现 t-{workspaceId}-{uuid} 格式的标签页ID管理和映射
 */
export class WorkonaTabManager {
  /**
   * 生成 Workona 风格标签页ID
   * 格式：t-{workspaceId}-{uuid}
   */
  static generateWorkonaTabId(workspaceId: string): string {
    // 生成 UUID v4 格式的随机ID
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    
    return `t-${workspaceId}-${uuid}`;
  }

  /**
   * 创建标签页ID映射关系（增强版：支持元数据分类）
   */
  static async createTabIdMapping(
    workonaId: string,
    chromeId: number,
    workspaceId: string,
    websiteId?: string,
    options?: {
      isWorkspaceCore?: boolean;
      source?: 'workspace_website' | 'user_opened' | 'session_restored';
      sessionId?: string;
      originalUrl?: string;
    }
  ): Promise<OperationResult<TabIdMapping>> {
    try {
      // 确定标签页类型和性质
      const isWorkspaceCore = options?.isWorkspaceCore ?? (!!websiteId);
      const tabType: 'core' | 'session' = isWorkspaceCore ? 'core' : 'session';
      const source = options?.source ?? (websiteId ? 'workspace_website' : 'user_opened');

      const mapping: TabIdMapping = {
        workonaId,
        chromeId,
        workspaceId,
        websiteId,
        createdAt: Date.now(),
        lastSyncAt: Date.now(),

        // 概念性重构：标签页分类元数据
        isWorkspaceCore,
        tabType,
        metadata: {
          source,
          sessionId: options?.sessionId,
          originalUrl: options?.originalUrl,
          addedToWorkspaceAt: isWorkspaceCore ? Date.now() : undefined
        }
      };

      // 获取现有映射
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;

      // 检查是否已存在相同的映射
      const existingIndex = mappings.findIndex(m =>
        m.workonaId === workonaId || m.chromeId === chromeId
      );

      if (existingIndex >= 0) {
        // 更新现有映射，保留重要元数据
        const existing = mappings[existingIndex];
        mappings[existingIndex] = {
          ...mapping,
          createdAt: existing.createdAt, // 保留原始创建时间
          metadata: {
            ...mapping.metadata,
            addedToWorkspaceAt: existing.metadata?.addedToWorkspaceAt || mapping.metadata?.addedToWorkspaceAt
          }
        };
      } else {
        // 添加新映射
        mappings.push(mapping);
      }

      // 保存映射
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 创建标签页ID映射: ${workonaId} <-> ${chromeId} (类型: ${tabType}, 核心: ${isWorkspaceCore})`);
      return { success: true, data: mapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab ID mapping',
          details: error,
        },
      };
    }
  }

  /**
   * 根据 Chrome ID 获取 Workona ID
   */
  static async getWorkonaIdByChromeId(chromeId: number): Promise<OperationResult<string | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.chromeId === chromeId);
      
      return { success: true, data: mapping?.workonaId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get Workona ID by Chrome ID',
          details: error,
        },
      };
    }
  }

  /**
   * 根据 Workona ID 获取 Chrome ID
   */
  static async getChromeIdByWorkonaId(workonaId: string): Promise<OperationResult<number | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.workonaId === workonaId);

      return { success: true, data: mapping?.chromeId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get Chrome ID by Workona ID',
          details: error,
        },
      };
    }
  }



  /**
   * 同步标签页映射关系
   * 清理无效的映射，更新现有映射的同步时间
   */
  static async syncTabMappings(): Promise<OperationResult<number>> {
    try {
      console.log('🔄 开始同步标签页映射关系...');
      
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const validMappings: TabIdMapping[] = [];
      let cleanedCount = 0;

      // 获取所有当前存在的标签页
      const allTabs = await chrome.tabs.query({});
      const existingChromeIds = new Set(allTabs.map(tab => tab.id!));

      // 检查每个映射的有效性
      for (const mapping of mappings) {
        if (existingChromeIds.has(mapping.chromeId)) {
          // 标签页仍然存在，更新同步时间
          mapping.lastSyncAt = Date.now();
          validMappings.push(mapping);
        } else {
          // 标签页已不存在，清理映射
          console.log(`🗑️ 清理无效映射: ${mapping.workonaId} <-> ${mapping.chromeId}`);
          cleanedCount++;
        }
      }

      // 保存清理后的映射
      const saveResult = await StorageManager.saveTabIdMappings(validMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 标签页映射同步完成，清理了 ${cleanedCount} 个无效映射`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync tab mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 删除标签页映射
   */
  static async removeTabMapping(workonaId: string): Promise<OperationResult<void>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const filteredMappings = mappings.filter(m => m.workonaId !== workonaId);

      const saveResult = await StorageManager.saveTabIdMappings(filteredMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🗑️ 删除标签页映射: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to remove tab mapping',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的所有 Workona 标签页ID
   */
  static async getWorkspaceWorkonaTabIds(workspaceId: string): Promise<OperationResult<string[]>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const workonaIds = mappings
        .filter(m => m.workspaceId === workspaceId)
        .map(m => m.workonaId);

      return { success: true, data: workonaIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace Workona tab IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 批量清理工作区的标签页映射
   */
  static async clearWorkspaceTabMappings(workspaceId: string): Promise<OperationResult<number>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const remainingMappings = mappings.filter(m => m.workspaceId !== workspaceId);
      const clearedCount = mappings.length - remainingMappings.length;

      const saveResult = await StorageManager.saveTabIdMappings(remainingMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🗑️ 清理工作区 ${workspaceId} 的 ${clearedCount} 个标签页映射`);
      return { success: true, data: clearedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to clear workspace tab mappings',
          details: error,
        },
      };
    }
  }

  // === 概念性重构：标签页元数据管理方法 ===

  /**
   * 获取标签页元数据
   */
  static async getTabMetadata(workonaId: string): Promise<OperationResult<TabIdMapping | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.workonaId === workonaId);

      return { success: true, data: mapping || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tab metadata',
          details: error,
        },
      };
    }
  }

  /**
   * 更新标签页元数据
   */
  static async updateTabMetadata(
    workonaId: string,
    updates: Partial<Pick<TabIdMapping, 'isWorkspaceCore' | 'tabType' | 'metadata'>>
  ): Promise<OperationResult<TabIdMapping>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mappingIndex = mappings.findIndex(m => m.workonaId === workonaId);

      if (mappingIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Tab mapping not found',
          },
        };
      }

      // 更新映射
      const existingMapping = mappings[mappingIndex];
      const updatedMapping: TabIdMapping = {
        ...existingMapping,
        ...updates,
        lastSyncAt: Date.now(),
        metadata: {
          ...existingMapping.metadata,
          ...updates.metadata,
          // 确保 source 字段始终有值
          source: updates.metadata?.source || existingMapping.metadata?.source || 'user_opened'
        }
      };

      mappings[mappingIndex] = updatedMapping;

      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`📝 更新标签页元数据: ${workonaId} (核心: ${updatedMapping.isWorkspaceCore})`);
      return { success: true, data: updatedMapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to update tab metadata',
          details: error,
        },
      };
    }
  }

  /**
   * 将会话临时标签页转换为工作区核心标签页
   */
  static async promoteToWorkspaceCore(workonaId: string, websiteId?: string): Promise<OperationResult<TabIdMapping>> {
    try {
      const updates: Partial<TabIdMapping> = {
        isWorkspaceCore: true,
        tabType: 'core',
        websiteId,
        metadata: {
          source: 'workspace_website',
          addedToWorkspaceAt: Date.now()
        }
      };

      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬆️ 标签页提升为工作区核心: ${workonaId}`);
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to promote tab to workspace core',
          details: error,
        },
      };
    }
  }

  /**
   * 将工作区核心标签页降级为会话临时标签页
   * 当用户从工作区中移除某个工作区专属标签页时调用
   */
  static async demoteToSessionTab(workonaId: string): Promise<OperationResult<TabIdMapping>> {
    try {
      // 获取当前标签页元数据
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Tab metadata not found for demotion',
          },
        };
      }

      const currentMetadata = metadataResult.data;

      // 只有工作区核心标签页才能降级
      if (!currentMetadata.isWorkspaceCore) {
        console.log(`⚠️ 标签页 ${workonaId} 已经是会话临时标签页，无需降级`);
        return { success: true, data: currentMetadata };
      }

      const updates: Partial<TabIdMapping> = {
        isWorkspaceCore: false,
        tabType: 'session',
        websiteId: undefined, // 移除与工作区网站的关联
        metadata: {
          ...currentMetadata.metadata,
          source: 'user_opened',
          addedToWorkspaceAt: undefined, // 移除工作区添加时间
          demotedAt: Date.now(), // 记录降级时间
          originalWebsiteId: currentMetadata.websiteId // 保留原始网站ID用于追踪
        }
      };

      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬇️ 标签页降级为会话临时标签页: ${workonaId} (原网站ID: ${currentMetadata.websiteId})`);
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to demote tab to session tab',
          details: error,
        },
      };
    }
  }

  /**
   * 同步标签页编辑后的状态
   * 确保编辑标签页后不会破坏 Workona ID 映射关系
   */
  static async syncTabAfterEdit(chromeId: number, newUrl?: string, newTitle?: string): Promise<OperationResult<void>> {
    try {
      // 获取标签页的 Workona ID
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 标签页没有 Workona ID，可能是系统标签页，跳过同步
        return { success: true };
      }

      const workonaId = workonaIdResult.data;

      // 获取当前标签页元数据
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        console.warn(`无法获取标签页元数据: ${workonaId}`);
        return { success: true };
      }

      const currentMetadata = metadataResult.data;

      // 更新元数据中的URL信息（如果有变化）
      if (newUrl && newUrl !== currentMetadata.metadata?.originalUrl) {
        const updates: Partial<TabIdMapping> = {
          metadata: {
            ...currentMetadata.metadata,
            source: currentMetadata.metadata?.source || 'user_opened',
            originalUrl: newUrl,
            // 记录编辑时间
            lastEditedAt: Date.now()
          }
        };

        const updateResult = await this.updateTabMetadata(workonaId, updates);
        if (updateResult.success) {
          console.log(`🔄 同步标签页编辑: ${workonaId} (新URL: ${newUrl})`);
        }
      }

      // 同步到会话管理器
      const { WorkspaceSessionManager } = await import('./workspaceSessionManager');
      const currentSession = WorkspaceSessionManager.getCurrentSession();

      if (currentSession && currentSession.tabs[workonaId]) {
        const updatedTab = {
          ...currentSession.tabs[workonaId],
          url: newUrl || currentSession.tabs[workonaId].url,
          title: newTitle || currentSession.tabs[workonaId].title,
          lastUpdated: Date.now()
        };

        await WorkspaceSessionManager.updateSessionTab(workonaId, updatedTab);
        console.log(`📝 同步标签页到会话: ${workonaId}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync tab after edit',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为工作区核心标签页
   */
  static async isWorkspaceCore(chromeId: number): Promise<OperationResult<boolean>> {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true, data: false }; // 无 Workona ID = 非核心标签页
      }

      const metadataResult = await this.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return { success: true, data: false };
      }

      return { success: true, data: metadataResult.data.isWorkspaceCore };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check if tab is workspace core',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的所有核心标签页 Workona ID
   */
  static async getWorkspaceCoreTabIds(workspaceId: string): Promise<OperationResult<string[]>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const coreTabIds = mappings
        .filter(m => m.workspaceId === workspaceId && m.isWorkspaceCore)
        .map(m => m.workonaId);

      return { success: true, data: coreTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace core tab IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的所有会话临时标签页 Workona ID
   */
  static async getWorkspaceSessionTabIds(workspaceId: string): Promise<OperationResult<string[]>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const sessionTabIds = mappings
        .filter(m => m.workspaceId === workspaceId && !m.isWorkspaceCore)
        .map(m => m.workonaId);

      return { success: true, data: sessionTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace session tab IDs',
          details: error,
        },
      };
    }
  }
}
